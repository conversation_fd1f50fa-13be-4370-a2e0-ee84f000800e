# 数据库快速参考

常用的数据库操作命令速查表。

## 🚀 日常操作

### 添加新字段或表

```bash
# 1. 修改 src/lib/db/schema.ts
# 2. 生成迁移
npm run db:generate

# 3. 应用迁移
npm run db:apply
```

### 查看数据库

```bash
# 查看所有表结构
npm run db:schema

# 查看迁移历史
npm run db:list

# 打开数据库管理界面
npm run db:studio:local   # 开发模式数据库
npm run db:studio         # 预览模式数据库
npm run db:studio:remote  # 生产环境数据库
```

### 备份数据库

```bash
npm run db:backup
```

## 🔄 重置数据库（谨慎使用）

```bash
# 完全重置本地数据库
npm run db:reset
```

## 🚀 生产部署

```bash
# 部署到生产环境
npm run db:apply:remote
npx wrangler deploy
```

## 📁 重要文件位置

- **数据库 Schema**: `src/lib/db/schema.ts`
- **迁移文件**: `migrations/`
- **本地数据库**: `.wrangler/state/v3/d1/miniflare-D1DatabaseObject/ai-next-template.sqlite`
- **配置文件**: `drizzle.config.ts`, `wrangler.jsonc`

详细说明请查看 [DATABASE_MANAGEMENT.md](./DATABASE_MANAGEMENT.md)
