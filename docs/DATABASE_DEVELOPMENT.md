# 数据库开发环境配置指南

## 概述

本项目现在使用**固定路径的数据库文件**来解决之前 wrangler 生成随机名称数据库文件的问题。这样可以确保开发环境的数据一致性，并方便使用外部数据库工具（如 DataGrip）进行管理。

## 数据库文件位置

### 固定位置（推荐）

```
local-database/ai-next-template.sqlite
```

### 旧的随机位置（已废弃）

```
.wrangler/state/v3/d1/miniflare-D1DatabaseObject/[随机哈希].sqlite
```

## 开发模式对比

| 模式         | 命令              | 数据库连接      | 用途                  |
| ------------ | ----------------- | --------------- | --------------------- |
| **开发模式** | `npm run dev`     | 固定本地 SQLite | 日常开发、样式调试    |
| **预览模式** | `npm run preview` | 本地 D1 API     | Cloudflare 兼容性测试 |
| **生产模式** | 部署后            | 远程 D1 API     | 生产环境              |

## 数据库架构优势

### ✅ 解决的问题

- **路径固定**：不再生成随机名称的数据库文件
- **数据一致性**：开发和预览模式共享同一数据库
- **工具兼容**：DataGrip 等工具可以直接连接固定路径
- **备份方便**：固定路径便于自动化备份

### 🚀 性能优化

- **better-sqlite3**：开发模式使用原生 SQLite，性能更好
- **自动迁移**：自动将旧的随机数据库迁移到固定位置
- **环境隔离**：不同环境使用不同的数据库连接方式

## 快速开始

### 1. 迁移现有数据（如果有的话）

```bash
npm run db:migrate
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 连接数据库工具

- **文件路径**：`./local-database/ai-next-template.sqlite`
- **工具推荐**：DataGrip, DB Browser for SQLite, SQLiteStudio

## 常用脚本

```bash
# 数据库迁移（将旧数据迁移到固定位置）
npm run db:migrate

# 查看数据库结构
npm run db:schema

# 备份数据库
npm run db:backup

# 重置数据库（清空并重新创建）
npm run db:reset

# 开发模式
npm run dev

# 预览模式（智能检测并自动应用迁移）
npm run preview

# 预览模式（简单版本，不自动迁移）
npm run preview:simple
```

## 数据库管理

### 手动操作

```bash
# 直接查看数据库
sqlite3 local-database/ai-next-template.sqlite

# 查看所有表
sqlite3 local-database/ai-next-template.sqlite ".tables"

# 查看表结构
sqlite3 local-database/ai-next-template.sqlite ".schema users"
```

### DataGrip 连接

1. 新建数据源 → SQLite
2. 文件路径：`项目根目录/local-database/ai-next-template.sqlite`
3. 测试连接
4. 应用设置

## 技术实现

### 自动环境检测

```typescript
// 开发环境：使用 better-sqlite3
if (process.env.NODE_ENV === "development") {
  return createLocalDB();
}

// 生产环境：使用 Cloudflare D1
const { env } = await getCloudflareContext({ async: true });
return createDB(env.DB);
```

### 数据库路径优先级

1. **固定路径**：`local-database/ai-next-template.sqlite`
2. **自动迁移**：从 wrangler 随机文件迁移
3. **新建文件**：如果都不存在则创建新的

## 预览模式数据库问题

### 问题说明

当您运行 `npm run preview` 时，可能会遇到数据库相关的错误，比如：

```
[auth][error] AdapterError: Failed query: select "session"."sessionToken"...
```

**原因：**

- `npm run dev` 使用 `local-database/ai-next-template.sqlite`（已有表结构）
- `npm run preview` 使用 `.wrangler/state/v3/d1/` 下的文件（可能是空的）
- Wrangler 只创建空数据库文件，不会自动应用迁移

### 解决方案

#### 方案 1：使用智能预览脚本（推荐）

```bash
npm run preview  # 自动检测并应用迁移
```

#### 方案 2：手动应用迁移

```bash
npm run preview:simple  # 启动预览但不自动迁移
npm run db:apply        # 手动应用迁移
```

#### 方案 3：重置数据库

```bash
npm run db:clean  # 彻底清理所有数据库文件（推荐）
npm run db:reset  # 重置并重新创建数据库
```

## 故障排除

### 问题：找不到数据库文件

```bash
# 检查文件是否存在
ls -la local-database/

# 重新运行迁移
npm run db:migrate
```

### 问题：数据库连接失败

```bash
# 检查文件权限
chmod 664 local-database/ai-next-template.sqlite

# 重新生成数据库
npm run db:reset
```

### 问题：重置数据库后仍能看到旧数据

**原因：** SQLite WAL (Write-Ahead Logging) 模式会创建额外的文件：

- `ai-next-template.sqlite` - 主数据库文件
- `ai-next-template.sqlite-wal` - WAL 文件（包含未提交的更改）
- `ai-next-template.sqlite-shm` - 共享内存文件

**解决方案：**

```bash
# 彻底清理所有 SQLite 文件
npm run db:clean

# 或手动清理
sqlite3 local-database/ai-next-template.sqlite "PRAGMA wal_checkpoint(TRUNCATE); PRAGMA journal_mode=DELETE;"
rm -f local-database/ai-next-template.sqlite-*
```

### 问题：DataGrip 显示缓存数据

**解决方案：**

1. 在 DataGrip 中右键数据库连接
2. 选择 "Refresh" 或 "Synchronize"
3. 或重新连接数据库

### 问题：DataGrip 无法连接

1. 确保文件路径正确
2. 检查文件权限
3. 尝试先用命令行 `sqlite3` 测试

## 最佳实践

### 1. 定期备份

```bash
# 手动备份
npm run db:backup

# 自动备份（可以加入 crontab）
cp local-database/ai-next-template.sqlite "backup/db_$(date +%Y%m%d_%H%M%S).sqlite"
```

### 2. 版本控制

- 数据库文件已加入 `.gitignore`
- 只提交 schema 和 migrations
- 不要提交实际数据

### 3. 团队开发

- 每个开发者都有自己的本地数据库
- 通过 migrations 同步数据库结构
- 测试数据通过 seed 脚本生成

## 下一步计划

- [ ] 添加数据库 seed 脚本
- [ ] 集成数据库备份到云存储
- [ ] 添加开发数据生成工具
- [ ] 优化数据库查询性能监控
